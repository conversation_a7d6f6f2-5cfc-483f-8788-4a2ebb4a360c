body {
    font-family: 'Arial', sans-serif;
    direction: rtl;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

header {
    background-color: white;
    color: black;
    padding: 10px 0;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.logo {
    width: 100px;
    height: auto;
    vertical-align: middle;
    margin-right: 10px;
}

header h1 {
    display: inline;
    font-size: 24px;
    vertical-align: middle;
}

nav ul {
    list-style: none;
    padding: 0;
    background-color: #003366;
    overflow: hidden;
    margin: 0;
}

nav ul li {
    float: right;
}

nav ul li a {
    display: block;
    color: white;
    text-align: center;
    padding: 14px 16px;
    text-decoration: none;
}

nav ul li a:hover {
    background-color: #FFA500;
}

main {
    padding: 20px;
}

.welcome {
    background-color: #4CAF50; /* لون أفضل لخلفية الترحيب */
    color: white;
    padding: 10px;
    text-align: center;
    font-size: 18px;
}

.stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
}

.card {
    background-color: #003366;
    color: white;
    padding: 20px;
    flex: 1;
    margin: 0 10px;
    text-align: center;
    border-radius: 5px;
}

.finances {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    text-align: center;
}

.finances p {
    margin: 10px 0;
}
