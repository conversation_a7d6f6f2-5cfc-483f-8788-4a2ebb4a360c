-- تحديث جدول المستخدمين ليشمل حقول إضافية
ALTER TABLE users
ADD COLUMN email VARCHAR(255) CHARACTER SET utf8mb4 AFTER name,
ADD COLUMN role VARCHAR(50) CHARACTER SET utf8mb4 DEFAULT 'user' AFTER email,
ADD COLUMN status VARCHAR(50) CHARACTER SET utf8mb4 DEFAULT 'active' AFTER role,
ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP AFTER status,
ADD COLUMN last_login DATETIME NULL AFTER created_at,
ADD COLUMN phone VARCHAR(50) CHARACTER SET utf8mb4 NULL AFTER last_login,
ADD COLUMN profile_image VARCHAR(255) NULL AFTER phone,
ADD UNIQUE INDEX idx_email (email),
ADD UNIQUE INDEX idx_user_name (user_name);

-- تحويل حقل كلمة المرور إلى UTF8 لدعم التشفير الحديث
ALTER TABLE users
MODIFY COLUMN password VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;