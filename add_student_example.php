<?php
include "db_conn.php";

// مثال لاستخدام الإجراء المخزن لإضافة طالب جديد
$ref_number = "STD-" . date('Y') . "-" . rand(1000, 9999);
$first_name_ar = "أحمد";
$last_name_ar = "محمد";
$first_name_fr = "<PERSON>";
$last_name_fr = "<PERSON>";
$gender = "ذكر";
$dob = "2010-05-15";
$address = "الرباط، المغرب";
$phone = "0612345678";
$email = "<EMAIL>";
$parent_id = null; // يمكن تحديده لاحقًا
$grade_level = "الصف الثالث";

$stmt = mysqli_prepare($conn, "CALL AddNewStudent(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
mysqli_stmt_bind_param($stmt, "ssssssssssss", 
    $ref_number, $first_name_ar, $last_name_ar, $first_name_fr, $last_name_fr,
    $gender, $dob, $address, $phone, $email, $parent_id, $grade_level);

if (mysqli_stmt_execute($stmt)) {
    echo "تمت إضافة الطالب بنجاح!";
} else {
    echo "حدث خطأ: " . mysqli_error($conn);
}

mysqli_stmt_close($stmt);