<?php

$sname = "localhost";
$unmae = "root";
$password = ""; // تغيير كلمة المرور حسب إعدادات الخادم

$db_name = "afaqcenter"; // اسم قاعدة البيانات الجديدة

$conn = mysqli_connect($sname, $unmae, $password, $db_name);

if (!$conn) {
    // إذا فشل الاتصال، حاول إنشاء قاعدة البيانات
    $conn_temp = mysqli_connect($sname, $unmae, $password);
    if ($conn_temp) {
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $create_db = "CREATE DATABASE IF NOT EXISTS `afaqcenter` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        mysqli_query($conn_temp, $create_db);
        mysqli_close($conn_temp);

        // إعادة محاولة الاتصال
        $conn = mysqli_connect($sname, $unmae, $password, $db_name);
    }

    if (!$conn) {
        die("فشل الاتصال بقاعدة البيانات: " . mysqli_connect_error());
    }
}

// تعيين ترميز الاتصال إلى UTF-8 لدعم اللغة العربية
mysqli_set_charset($conn, "utf8mb4");

// التحقق من وجود جدول المستخدمين وإنشاؤه إذا لم يكن موجوداً
$check_table = "SHOW TABLES LIKE 'users'";
$result = mysqli_query($conn, $check_table);

if (mysqli_num_rows($result) == 0) {
    // إنشاء جدول المستخدمين الأساسي
    $create_users_table = "
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_name` varchar(100) NOT NULL,
            `password` varchar(255) NOT NULL,
            `name` varchar(200) NOT NULL,
            `email` varchar(150) DEFAULT NULL,
            `role` enum('admin','teacher','student','staff') DEFAULT 'staff',
            `status` enum('active','inactive','suspended') DEFAULT 'active',
            `last_login` datetime DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `user_name` (`user_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $create_users_table);

    // إنشاء جدول محاولات تسجيل الدخول
    $create_login_attempts_table = "
        CREATE TABLE IF NOT EXISTS `login_attempts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(100) DEFAULT NULL,
            `ip_address` varchar(45) NOT NULL,
            `success` tinyint(1) NOT NULL DEFAULT 0,
            `attempt_time` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `ip_address` (`ip_address`),
            KEY `attempt_time` (`attempt_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $create_login_attempts_table);

    // إنشاء جدول رموز التذكر
    $create_remember_tokens_table = "
        CREATE TABLE IF NOT EXISTS `remember_tokens` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `token` varchar(255) NOT NULL,
            `expires` datetime NOT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `user_id` (`user_id`),
            UNIQUE KEY `token` (`token`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $create_remember_tokens_table);

    // إنشاء جدول الطلاب
    $create_students_table = "
        CREATE TABLE IF NOT EXISTS `students` (
            `student_id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `student_code` varchar(20) UNIQUE DEFAULT NULL,
            `first_name` varchar(100) NOT NULL,
            `last_name` varchar(100) NOT NULL,
            `first_name_fr` varchar(100) DEFAULT NULL,
            `last_name_fr` varchar(100) DEFAULT NULL,
            `date_of_birth` date NOT NULL,
            `gender` enum('ذكر','أنثى') NOT NULL,
            `contact_info` varchar(200) DEFAULT NULL,
            `parent_phone` varchar(20) DEFAULT NULL,
            `parent_email` varchar(150) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `enrollment_date` date NOT NULL,
            `grade_level` varchar(50) DEFAULT NULL,
            `status` enum('active','inactive','graduated','transferred') DEFAULT 'active',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`student_id`),
            UNIQUE KEY `student_code` (`student_code`),
            KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $create_students_table);

    // إنشاء جدول الأساتذة
    $create_teachers_table = "
        CREATE TABLE IF NOT EXISTS `teachers` (
            `teacher_id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `first_name` varchar(100) NOT NULL,
            `last_name` varchar(100) NOT NULL,
            `first_name_fr` varchar(100) DEFAULT NULL,
            `last_name_fr` varchar(100) DEFAULT NULL,
            `date_of_birth` date DEFAULT NULL,
            `gender` enum('ذكر','أنثى') NOT NULL,
            `contact_info` varchar(200) DEFAULT NULL,
            `email` varchar(150) DEFAULT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `hire_date` date NOT NULL,
            `subject_specialization` varchar(100) DEFAULT NULL,
            `salary` decimal(10,2) DEFAULT NULL,
            `hourly_rate` decimal(10,2) DEFAULT NULL,
            `status` enum('active','inactive','on_leave') DEFAULT 'active',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`teacher_id`),
            UNIQUE KEY `email` (`email`),
            KEY `user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $create_teachers_table);

    // إنشاء جدول الأقسام
    $create_departments_table = "
        CREATE TABLE IF NOT EXISTS `departments` (
            `department_id` int(11) NOT NULL AUTO_INCREMENT,
            `department_name` varchar(100) NOT NULL,
            `department_name_fr` varchar(100) DEFAULT NULL,
            `description` text DEFAULT NULL,
            `head_teacher_id` int(11) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`department_id`),
            UNIQUE KEY `department_name` (`department_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $create_departments_table);

    // إنشاء جدول المواد
    $create_subjects_table = "
        CREATE TABLE IF NOT EXISTS `subjects` (
            `subject_id` int(11) NOT NULL AUTO_INCREMENT,
            `subject_name` varchar(100) NOT NULL,
            `subject_name_fr` varchar(100) DEFAULT NULL,
            `subject_code` varchar(20) UNIQUE DEFAULT NULL,
            `department_id` int(11) DEFAULT NULL,
            `description` text DEFAULT NULL,
            `credits` int(11) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`subject_id`),
            UNIQUE KEY `subject_code` (`subject_code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $create_subjects_table);

    // إنشاء جدول الفصول
    $create_classes_table = "
        CREATE TABLE IF NOT EXISTS `classes` (
            `class_id` int(11) NOT NULL AUTO_INCREMENT,
            `class_name` varchar(100) NOT NULL,
            `class_code` varchar(20) UNIQUE DEFAULT NULL,
            `teacher_id` int(11) DEFAULT NULL,
            `subject_id` int(11) NOT NULL,
            `room_number` varchar(20) DEFAULT NULL,
            `schedule_time` time DEFAULT NULL,
            `schedule_day` enum('الأحد','الإثنين','الثلاثاء','الأربعاء','الخميس','الجمعة','السبت') DEFAULT NULL,
            `duration_minutes` int(11) DEFAULT 60,
            `academic_year` varchar(20) DEFAULT NULL,
            `semester` enum('الفصل الأول','الفصل الثاني','الفصل الثالث') DEFAULT 'الفصل الأول',
            `status` enum('active','inactive','completed') DEFAULT 'active',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`class_id`),
            UNIQUE KEY `class_code` (`class_code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $create_classes_table);
}

// إضافة عمود role إذا لم يكن موجوداً
$check_role_column = "SHOW COLUMNS FROM users LIKE 'role'";
$result = mysqli_query($conn, $check_role_column);
if (mysqli_num_rows($result) == 0) {
    $add_role_column = "ALTER TABLE users ADD COLUMN role enum('admin','teacher','student','staff') DEFAULT 'staff' AFTER name";
    mysqli_query($conn, $add_role_column);
}

// إضافة عمود status إذا لم يكن موجوداً
$check_status_column = "SHOW COLUMNS FROM users LIKE 'status'";
$result = mysqli_query($conn, $check_status_column);
if (mysqli_num_rows($result) == 0) {
    $add_status_column = "ALTER TABLE users ADD COLUMN status enum('active','inactive','suspended') DEFAULT 'active' AFTER role";
    mysqli_query($conn, $add_status_column);
}

// إضافة عمود email إذا لم يكن موجوداً
$check_email_column = "SHOW COLUMNS FROM users LIKE 'email'";
$result = mysqli_query($conn, $check_email_column);
if (mysqli_num_rows($result) == 0) {
    $add_email_column = "ALTER TABLE users ADD COLUMN email varchar(150) DEFAULT NULL AFTER name";
    mysqli_query($conn, $add_email_column);
}