<?php
session_start();

// Include database connection
include "db_conn.php";

// Security: Check if admin already exists
$check_admin_sql = "SELECT COUNT(*) as admin_count FROM users WHERE role = 'admin' OR id = 1";
$result = mysqli_query($conn, $check_admin_sql);
$admin_exists = false;

if ($result) {
    $row = mysqli_fetch_assoc($result);
    $admin_exists = $row['admin_count'] > 0;
}

// If admin exists, redirect to login
if ($admin_exists) {
    header("Location: login_professional.php?error=المدير موجود بالفعل. يرجى تسجيل الدخول");
    exit();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate and sanitize input
    function validate($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        return $data;
    }
    
    $admin_name = validate($_POST['admin_name']);
    $username = validate($_POST['username']);
    $email = validate($_POST['email']);
    $password = validate($_POST['password']);
    $confirm_password = validate($_POST['confirm_password']);
    $setup_key = validate($_POST['setup_key']);
    
    $errors = [];
    
    // Validation
    if (empty($admin_name)) {
        $errors[] = "اسم المدير مطلوب";
    }
    
    if (empty($username)) {
        $errors[] = "اسم المستخدم مطلوب";
    } elseif (strlen($username) < 3) {
        $errors[] = "اسم المستخدم يجب أن يكون 3 أحرف على الأقل";
    }
    
    if (empty($email)) {
        $errors[] = "البريد الإلكتروني مطلوب";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "البريد الإلكتروني غير صحيح";
    }
    
    if (empty($password)) {
        $errors[] = "كلمة المرور مطلوبة";
    } elseif (strlen($password) < 8) {
        $errors[] = "كلمة المرور يجب أن تكون 8 أحرف على الأقل";
    } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $password)) {
        $errors[] = "كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص";
    }
    
    if ($password !== $confirm_password) {
        $errors[] = "كلمة المرور وتأكيدها غير متطابقتين";
    }
    
    // Simple setup key validation (you can make this more complex)
    $expected_setup_key = "ADMIN_SETUP_2024"; // Change this to your preferred setup key
    if ($setup_key !== $expected_setup_key) {
        $errors[] = "مفتاح الإعداد غير صحيح";
    }
    
    // Check if username already exists
    if (empty($errors)) {
        $check_user_sql = "SELECT id FROM users WHERE user_name = ?";
        $stmt = mysqli_prepare($conn, $check_user_sql);
        mysqli_stmt_bind_param($stmt, "s", $username);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if (mysqli_num_rows($result) > 0) {
            $errors[] = "اسم المستخدم موجود بالفعل";
        }
        mysqli_stmt_close($stmt);
    }
    
    // If no errors, create admin account
    if (empty($errors)) {
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        $insert_sql = "INSERT INTO users (user_name, password, name, email, role, status, created_at) VALUES (?, ?, ?, ?, 'admin', 'active', NOW())";
        $stmt = mysqli_prepare($conn, $insert_sql);
        
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "ssss", $username, $hashed_password, $admin_name, $email);
            
            if (mysqli_stmt_execute($stmt)) {
                // إنشاء جلسة للمستخدم مباشرة
                $_SESSION['id'] = mysqli_insert_id($conn);
                $_SESSION['user_name'] = $username;
                $_SESSION['name'] = $admin_name;
                $_SESSION['role'] = 'admin';
                
                // تسجيل أول دخول
                $update_login = "UPDATE users SET last_login = NOW() WHERE id = ?";
                $login_stmt = mysqli_prepare($conn, $update_login);
                mysqli_stmt_bind_param($login_stmt, "i", $_SESSION['id']);
                mysqli_stmt_execute($login_stmt);
                mysqli_stmt_close($login_stmt);
                
                // توجيه إلى لوحة التحكم مع رسالة ترحيب
                mysqli_stmt_close($stmt);
                header("Location: index.php?welcome=1");
                exit();
            } else {
                $errors[] = "حدث خطأ أثناء إنشاء الحساب";
            }
            mysqli_stmt_close($stmt);
        } else {
            $errors[] = "خطأ في قاعدة البيانات";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد حساب المدير - مركز آفاق التعليمي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-text: #2c3e50;
            --light-text: #7f8c8d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            padding: 20px;
        }

        .setup-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
            padding: 40px;
        }

        .setup-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .setup-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .setup-title {
            color: var(--primary-color);
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .setup-subtitle {
            color: var(--light-text);
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .warning-box {
            background: rgba(243, 156, 18, 0.1);
            border: 2px solid var(--warning-color);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .warning-box i {
            font-size: 2rem;
            color: var(--warning-color);
            margin-bottom: 10px;
        }

        .warning-text {
            color: var(--warning-color);
            font-weight: 600;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark-text);
            font-weight: 600;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
            direction: rtl;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .input-icon {
            position: absolute;
            top: 50%;
            right: 18px;
            transform: translateY(-50%);
            color: var(--light-text);
            font-size: 1.1rem;
        }

        .password-strength {
            margin-top: 8px;
            font-size: 0.85rem;
        }

        .strength-bar {
            height: 4px;
            background: #e1e8ed;
            border-radius: 2px;
            margin-top: 5px;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: var(--accent-color); width: 25%; }
        .strength-medium { background: var(--warning-color); width: 50%; }
        .strength-strong { background: var(--success-color); width: 75%; }
        .strength-very-strong { background: var(--success-color); width: 100%; }

        .btn-setup {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, var(--success-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(39, 174, 96, 0.3);
        }

        .btn-setup:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: none;
            font-weight: 500;
        }

        .alert-danger {
            background: rgba(231, 76, 60, 0.1);
            color: var(--accent-color);
            border-right: 4px solid var(--accent-color);
        }

        .requirements {
            background: rgba(52, 152, 219, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .requirements h5 {
            color: var(--secondary-color);
            margin-bottom: 15px;
            font-weight: 600;
        }

        .requirements ul {
            list-style: none;
            padding: 0;
        }

        .requirements li {
            padding: 5px 0;
            color: var(--dark-text);
            font-size: 0.9rem;
        }

        .requirements li i {
            color: var(--secondary-color);
            margin-left: 8px;
        }

        .setup-key-info {
            background: rgba(231, 76, 60, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .setup-key-info strong {
            color: var(--accent-color);
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .setup-container {
                margin: 10px;
                padding: 30px 20px;
            }

            .setup-title {
                font-size: 1.8rem;
            }

            .setup-icon {
                font-size: 3rem;
            }
        }

        /* Animation */
        .form-group {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.2s; }
        .form-group:nth-child(3) { animation-delay: 0.3s; }
        .form-group:nth-child(4) { animation-delay: 0.4s; }
        .form-group:nth-child(5) { animation-delay: 0.5s; }
        .form-group:nth-child(6) { animation-delay: 0.6s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <div class="setup-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <h1 class="setup-title">إعداد حساب المدير</h1>
            <p class="setup-subtitle">
                مرحباً بك في مركز آفاق التعليمي<br>
                يرجى إنشاء حساب المدير الرئيسي للنظام
            </p>
        </div>

        <div class="warning-box">
            <i class="fas fa-exclamation-triangle"></i>
            <div class="warning-text">
                تحذير: هذه الصفحة تظهر فقط عند الإعداد الأولي للنظام
            </div>
        </div>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <ul style="margin: 0; padding-right: 20px;">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <div class="requirements">
            <h5><i class="fas fa-info-circle"></i> متطلبات كلمة المرور:</h5>
            <ul>
                <li><i class="fas fa-check"></i> 8 أحرف على الأقل</li>
                <li><i class="fas fa-check"></i> حرف كبير واحد على الأقل (A-Z)</li>
                <li><i class="fas fa-check"></i> حرف صغير واحد على الأقل (a-z)</li>
                <li><i class="fas fa-check"></i> رقم واحد على الأقل (0-9)</li>
                <li><i class="fas fa-check"></i> رمز خاص واحد على الأقل (@$!%*?&)</li>
            </ul>
        </div>

        <div class="setup-key-info">
            <strong>مفتاح الإعداد المطلوب: ADMIN_SETUP_2024</strong>
        </div>

        <form method="POST" id="setupForm">
            <div class="form-group">
                <label for="admin_name" class="form-label">اسم المدير الكامل</label>
                <div style="position: relative;">
                    <input type="text" 
                           id="admin_name" 
                           name="admin_name" 
                           class="form-control" 
                           placeholder="أدخل اسم المدير الكامل"
                           value="<?php echo isset($_POST['admin_name']) ? htmlspecialchars($_POST['admin_name']) : ''; ?>"
                           required>
                    <i class="fas fa-user input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="username" class="form-label">اسم المستخدم</label>
                <div style="position: relative;">
                    <input type="text" 
                           id="username" 
                           name="username" 
                           class="form-control" 
                           placeholder="أدخل اسم المستخدم"
                           value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                           required>
                    <i class="fas fa-at input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <div style="position: relative;">
                    <input type="email" 
                           id="email" 
                           name="email" 
                           class="form-control" 
                           placeholder="أدخل البريد الإلكتروني"
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                           required>
                    <i class="fas fa-envelope input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور</label>
                <div style="position: relative;">
                    <input type="password" 
                           id="password" 
                           name="password" 
                           class="form-control" 
                           placeholder="أدخل كلمة المرور"
                           required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <div id="strengthText" style="margin-top: 5px;"></div>
                </div>
            </div>

            <div class="form-group">
                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                <div style="position: relative;">
                    <input type="password" 
                           id="confirm_password" 
                           name="confirm_password" 
                           class="form-control" 
                           placeholder="أعد إدخال كلمة المرور"
                           required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
                <div id="passwordMatch" style="margin-top: 5px; font-size: 0.85rem;"></div>
            </div>

            <div class="form-group">
                <label for="setup_key" class="form-label">مفتاح الإعداد</label>
                <div style="position: relative;">
                    <input type="text" 
                           id="setup_key" 
                           name="setup_key" 
                           class="form-control" 
                           placeholder="أدخل مفتاح الإعداد"
                           required>
                    <i class="fas fa-key input-icon"></i>
                </div>
            </div>

            <button type="submit" class="btn-setup" id="setupBtn">
                <i class="fas fa-user-plus me-2"></i>
                إنشاء حساب المدير
            </button>
        </form>
    </div>

    <script>
        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;
            let feedback = [];

            if (password.length >= 8) strength++;
            else feedback.push('8 أحرف على الأقل');

            if (/[a-z]/.test(password)) strength++;
            else feedback.push('حرف صغير');

            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('حرف كبير');

            if (/[0-9]/.test(password)) strength++;
            else feedback.push('رقم');

            if (/[@$!%*?&]/.test(password)) strength++;
            else feedback.push('رمز خاص');

            return { strength, feedback };
        }

        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const result = checkPasswordStrength(password);
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');

            // Remove all strength classes
            strengthFill.className = 'strength-fill';

            if (password.length === 0) {
                strengthText.textContent = '';
                return;
            }

            switch (result.strength) {
                case 1:
                case 2:
                    strengthFill.classList.add('strength-weak');
                    strengthText.innerHTML = '<span style="color: #e74c3c;">ضعيفة</span>';
                    break;
                case 3:
                    strengthFill.classList.add('strength-medium');
                    strengthText.innerHTML = '<span style="color: #f39c12;">متوسطة</span>';
                    break;
                case 4:
                    strengthFill.classList.add('strength-strong');
                    strengthText.innerHTML = '<span style="color: #27ae60;">قوية</span>';
                    break;
                case 5:
                    strengthFill.classList.add('strength-very-strong');
                    strengthText.innerHTML = '<span style="color: #27ae60;">قوية</span>';
                    break;
                default:
                    strengthText.innerHTML = '<span style="color: #e74c3c;">ضعيفة</span>';
            }

            if (result.feedback.length > 0) {
                strengthText.innerHTML += '<br><small>مطلوب: ' + result.feedback.join(', ') + '</small>';
            }
        });

        // Password match checker
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            const matchDiv = document.getElementById('passwordMatch');

            if (confirmPassword.length === 0) {
                matchDiv.textContent = '';
                return;
            }

            if (password === confirmPassword) {
                matchDiv.innerHTML = '<span style="color: #27ae60;"><i class="fas fa-check"></i> كلمة المرور متطابقة</span>';
            } else {
                matchDiv.innerHTML = '<span style="color: #e74c3c;"><i class="fas fa-times"></i> كلمة المرور غير متطابقة</span>';
            }
        });

        // Form validation
        document.getElementById('setupForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const result = checkPasswordStrength(password);

            if (result.strength < 4) {
                e.preventDefault();
                alert('كلمة المرور ضعيفة. يرجى اختيار كلمة مرور أقوى');
                return;
            }

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور وتأكيدها غير متطابقتين');
                return;
            }

            // Show loading state
            const btn = document.getElementById('setupBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إنشاء الحساب...';
        });
    </script>
</body>
</html>

