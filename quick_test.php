<?php
// اختبار سريع للنظام
include "db_conn.php";

echo "<h2>اختبار سريع للنظام</h2>";

// اختبار الاتصال
if ($conn) {
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    echo "<p>قاعدة البيانات: afaqcenter</p>";
    
    // عرض الجداول الموجودة
    $tables_query = "SHOW TABLES";
    $result = mysqli_query($conn, $tables_query);
    
    echo "<h3>الجداول الموجودة:</h3>";
    echo "<ul>";
    while ($table = mysqli_fetch_array($result)) {
        echo "<li>" . $table[0] . "</li>";
    }
    echo "</ul>";
    
    // اختبار عدد المستخدمين
    $users_query = "SELECT COUNT(*) as count FROM users";
    $result = mysqli_query($conn, $users_query);
    if ($result) {
        $count = mysqli_fetch_assoc($result)['count'];
        echo "<p>عدد المستخدمين: $count</p>";
    }
    
    // اختبار الجداول الأخرى
    $tables_to_check = ['students', 'teachers', 'classes', 'login_attempts'];
    
    foreach ($tables_to_check as $table) {
        $check_table = "SHOW TABLES LIKE '$table'";
        $result = mysqli_query($conn, $check_table);
        
        if (mysqli_num_rows($result) > 0) {
            $count_query = "SELECT COUNT(*) as count FROM $table";
            $count_result = mysqli_query($conn, $count_query);
            if ($count_result) {
                $count = mysqli_fetch_assoc($count_result)['count'];
                echo "<p style='color: green;'>✅ جدول $table موجود - العدد: $count</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ جدول $table غير موجود</p>";
        }
    }
    
} else {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>صفحة تسجيل الدخول</a></p>";
echo "<p><a href='setup_admin.php'>إعداد المدير</a></p>";
echo "<p><a href='admin.php'>لوحة تحكم المدير</a></p>";
?>
