<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_name'])) {
    header("Location: index.php?error=يجب تسجيل الدخول أولاً");
    exit();
}

// Include database connection
include "db_conn.php";

// Check if user is admin
$user_id = $_SESSION['user_id'];
$check_admin_sql = "SELECT role FROM users WHERE id = ? AND role = 'admin'";
$stmt = mysqli_prepare($conn, $check_admin_sql);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    header("Location: home.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة");
    exit();
}

// Get statistics
$stats = [];

// Function to safely get count from table
function getTableCount($conn, $table, $condition = '') {
    $check_table = "SHOW TABLES LIKE '$table'";
    $result = mysqli_query($conn, $check_table);

    if (mysqli_num_rows($result) > 0) {
        $sql = "SELECT COUNT(*) as count FROM $table";
        if ($condition) {
            $sql .= " WHERE $condition";
        }
        $result = mysqli_query($conn, $sql);
        if ($result) {
            return mysqli_fetch_assoc($result)['count'];
        }
    }
    return 0;
}

// Count users
$stats['users'] = getTableCount($conn, 'users');

// Count students
$stats['students'] = getTableCount($conn, 'students', "status = 'active'");

// Count teachers
$stats['teachers'] = getTableCount($conn, 'teachers', "status = 'active'");

// Count classes
$stats['classes'] = getTableCount($conn, 'classes', "status = 'active'");

// Get recent login attempts
$recent_attempts = null;
$check_login_attempts = "SHOW TABLES LIKE 'login_attempts'";
$result = mysqli_query($conn, $check_login_attempts);

if (mysqli_num_rows($result) > 0) {
    $recent_attempts_sql = "SELECT username, ip_address, success, attempt_time
                           FROM login_attempts
                           ORDER BY attempt_time DESC
                           LIMIT 10";
    $recent_attempts = mysqli_query($conn, $recent_attempts_sql);
}

// Get system info
$system_info = [
    'php_version' => phpversion(),
    'mysql_version' => mysqli_get_server_info($conn),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit')
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - مركز آفاق التعليمي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --light-bg: #ecf0f1;
            --dark-text: #2c3e50;
            --light-text: #7f8c8d;
            --sidebar-width: 280px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--light-bg);
            direction: rtl;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h3 {
            margin: 10px 0 5px 0;
            font-weight: 700;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            display: block;
            padding: 15px 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .menu-item:hover,
        .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
            border-right-color: white;
            color: white;
        }

        .menu-item i {
            width: 20px;
            margin-left: 10px;
        }

        .main-content {
            margin-right: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }

        .top-bar {
            background: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }

        .stat-icon.users { background: var(--info-color); }
        .stat-icon.students { background: var(--success-color); }
        .stat-icon.teachers { background: var(--warning-color); }
        .stat-icon.classes { background: var(--accent-color); }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--light-text);
            font-size: 0.9rem;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 20px 25px;
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .card-body {
            padding: 25px;
        }

        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }

        .table {
            margin: 0;
        }

        .table th {
            background: var(--light-bg);
            border: none;
            font-weight: 600;
            color: var(--dark-text);
        }

        .table td {
            border: none;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .badge {
            font-size: 0.75rem;
            padding: 5px 10px;
        }

        .badge.success { background: var(--success-color); }
        .badge.danger { background: var(--accent-color); }

        .system-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .info-label {
            font-weight: 600;
            color: var(--dark-text);
        }

        .info-value {
            color: var(--light-text);
        }

        .btn-admin {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .alert-admin {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .alert-info {
            background: rgba(23, 162, 184, 0.1);
            color: var(--info-color);
            border-right: 4px solid var(--info-color);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        .mobile-toggle {
            display: none;
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 5px;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
        }

        @media (max-width: 768px) {
            .mobile-toggle {
                display: block;
            }
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .action-btn {
            padding: 10px 15px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn.primary {
            background: var(--secondary-color);
            color: white;
        }

        .action-btn.success {
            background: var(--success-color);
            color: white;
        }

        .action-btn.warning {
            background: var(--warning-color);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-graduation-cap" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <h3>مركز آفاق التعليمي</h3>
            <p>لوحة تحكم المدير</p>
        </div>

        <nav class="sidebar-menu">
            <a href="admin.php" class="menu-item active">
                <i class="fas fa-tachometer-alt"></i>
                الرئيسية
            </a>
            <a href="manage_users.php" class="menu-item">
                <i class="fas fa-users"></i>
                إدارة المستخدمين
            </a>
            <a href="manage_students.php" class="menu-item">
                <i class="fas fa-user-graduate"></i>
                إدارة الطلاب
            </a>
            <a href="manage_teachers.php" class="menu-item">
                <i class="fas fa-chalkboard-teacher"></i>
                إدارة الأساتذة
            </a>
            <a href="manage_classes.php" class="menu-item">
                <i class="fas fa-school"></i>
                إدارة الفصول
            </a>
            <a href="manage_subjects.php" class="menu-item">
                <i class="fas fa-book"></i>
                إدارة المواد
            </a>
            <a href="manage_grades.php" class="menu-item">
                <i class="fas fa-chart-line"></i>
                إدارة الدرجات
            </a>
            <a href="manage_attendance.php" class="menu-item">
                <i class="fas fa-calendar-check"></i>
                إدارة الحضور
            </a>
            <a href="manage_invoices.php" class="menu-item">
                <i class="fas fa-file-invoice-dollar"></i>
                إدارة الفواتير
            </a>
            <a href="reports.php" class="menu-item">
                <i class="fas fa-chart-bar"></i>
                التقارير
            </a>
            <a href="settings.php" class="menu-item">
                <i class="fas fa-cog"></i>
                الإعدادات
            </a>
            <a href="backup.php" class="menu-item">
                <i class="fas fa-database"></i>
                النسخ الاحتياطي
            </a>
            <a href="logout.php" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <h1 class="page-title">لوحة تحكم المدير</h1>
            <div class="user-info">
                <span>مرحباً، <?php echo htmlspecialchars($_SESSION['name']); ?></span>
                <div class="user-avatar">
                    <?php echo strtoupper(substr($_SESSION['name'], 0, 1)); ?>
                </div>
            </div>
        </div>

        <!-- Alert -->
        <div class="alert-admin alert-info">
            <i class="fas fa-info-circle me-2"></i>
            مرحباً بك في لوحة تحكم مركز آفاق التعليمي. يمكنك من هنا إدارة جميع جوانب النظام التعليمي.
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="add_student.php" class="action-btn primary">
                <i class="fas fa-user-plus"></i>
                إضافة طالب جديد
            </a>
            <a href="add_teacher.php" class="action-btn success">
                <i class="fas fa-chalkboard-teacher"></i>
                إضافة أستاذ جديد
            </a>
            <a href="create_class.php" class="action-btn warning">
                <i class="fas fa-plus-circle"></i>
                إنشاء فصل جديد
            </a>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon users">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo $stats['users']; ?></div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon students">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="stat-number"><?php echo $stats['students']; ?></div>
                <div class="stat-label">الطلاب النشطون</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon teachers">
                    <i class="fas fa-chalkboard-teacher"></i>
                </div>
                <div class="stat-number"><?php echo $stats['teachers']; ?></div>
                <div class="stat-label">الأساتذة النشطون</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon classes">
                    <i class="fas fa-school"></i>
                </div>
                <div class="stat-number"><?php echo $stats['classes']; ?></div>
                <div class="stat-label">الفصول النشطة</div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- Recent Login Attempts -->
            <div class="content-card">
                <div class="card-header">
                    <i class="fas fa-shield-alt me-2"></i>
                    محاولات تسجيل الدخول الأخيرة
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم المستخدم</th>
                                    <th>عنوان IP</th>
                                    <th>الحالة</th>
                                    <th>الوقت</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($recent_attempts && mysqli_num_rows($recent_attempts) > 0): ?>
                                    <?php while ($attempt = mysqli_fetch_assoc($recent_attempts)): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($attempt['username'] ?: 'غير محدد'); ?></td>
                                        <td><?php echo htmlspecialchars($attempt['ip_address']); ?></td>
                                        <td>
                                            <span class="badge <?php echo $attempt['success'] ? 'success' : 'danger'; ?>">
                                                <?php echo $attempt['success'] ? 'نجح' : 'فشل'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($attempt['attempt_time'])); ?></td>
                                    </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            <i class="fas fa-info-circle me-2"></i>
                                            لا توجد محاولات تسجيل دخول بعد
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="content-card">
                <div class="card-header">
                    <i class="fas fa-server me-2"></i>
                    معلومات النظام
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-item">
                            <span class="info-label">إصدار PHP:</span>
                            <span class="info-value"><?php echo $system_info['php_version']; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">إصدار MySQL:</span>
                            <span class="info-value"><?php echo $system_info['mysql_version']; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">خادم الويب:</span>
                            <span class="info-value"><?php echo $system_info['server_software']; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">حد رفع الملفات:</span>
                            <span class="info-value"><?php echo $system_info['upload_max_filesize']; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">حد POST:</span>
                            <span class="info-value"><?php echo $system_info['post_max_size']; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">حد الذاكرة:</span>
                            <span class="info-value"><?php echo $system_info['memory_limit']; ?></span>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <button class="btn-admin" onclick="checkSystemHealth()">
                            <i class="fas fa-heartbeat me-2"></i>
                            فحص صحة النظام
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="content-card">
            <div class="card-header">
                <i class="fas fa-chart-pie me-2"></i>
                إحصائيات سريعة
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div>
                        <canvas id="usersChart" width="300" height="200"></canvas>
                    </div>
                    <div>
                        <canvas id="attendanceChart" width="300" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.querySelector('.mobile-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        // System health check
        function checkSystemHealth() {
            alert('جاري فحص صحة النظام...\n\n✅ قاعدة البيانات: متصلة\n✅ خادم الويب: يعمل\n✅ PHP: يعمل بشكل طبيعي\n✅ الذاكرة: متاحة\n\nالنظام يعمل بشكل طبيعي!');
        }

        // Initialize Charts
        document.addEventListener('DOMContentLoaded', function() {
            // Users Chart
            const usersCtx = document.getElementById('usersChart').getContext('2d');
            new Chart(usersCtx, {
                type: 'doughnut',
                data: {
                    labels: ['الطلاب', 'الأساتذة', 'الإدارة'],
                    datasets: [{
                        data: [<?php echo $stats['students']; ?>, <?php echo $stats['teachers']; ?>, <?php echo $stats['users'] - $stats['students'] - $stats['teachers']; ?>],
                        backgroundColor: ['#27ae60', '#f39c12', '#3498db'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'توزيع المستخدمين'
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Attendance Chart (Sample data)
            const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
            new Chart(attendanceCtx, {
                type: 'line',
                data: {
                    labels: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'],
                    datasets: [{
                        label: 'نسبة الحضور %',
                        data: [85, 92, 88, 94, 90],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'نسبة الحضور الأسبوعية'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        });

        // Auto-refresh stats every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
