// script.js
document.addEventListener('DOMContentLoaded', () => {
    const modal = document.getElementById('modal');
    const addButton = document.getElementById('addButton');
    const closeModalButtons = document.querySelectorAll('.close');
    const studentTableBody = document.getElementById('studentTableBody');
    const addStudentForm = document.getElementById('addStudentForm');

    addButton.onclick = () => {
        modal.style.display = 'block';
    };

    closeModalButtons.forEach(button => {
        button.onclick = () => {
            modal.style.display = 'none';
        };
    });

    window.onclick = (event) => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    };

    addStudentForm.onsubmit = (event) => {
        event.preventDefault();

        const firstName = document.getElementById('firstName').value;
        const lastName = document.getElementById('lastName').value;
        const firstNameFr = document.getElementById('firstNameFr').value;
        const lastNameFr = document.getElementById('lastNameFr').value;
        const gender = document.querySelector('input[name="gender"]:checked').value;
        const birthdate = document.getElementById('birthdate').value;
        const contactInfo = document.getElementById('contactInfo').value;

        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td>المثال المرجعي</td>
            <td>${lastName}</td>
            <td>${firstName}</td>
            <td>${contactInfo}</td>
            <td>الإجراءات</td>
        `;
        studentTableBody.appendChild(newRow);

        modal.style.display = 'none';
        addStudentForm.reset();
    };
});
