@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Cairo', sans-serif;
}

body {
    background-color: #f3f7fa;
    text-align: right;
    direction: rtl;
}

.container {
    width: 80%;
    margin: auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-top: 50px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #3e4a89;
    color: white;
    border-bottom: 1px solid #ddd;
    direction: ltr;
}

.header-left, .header-right {
    display: flex;
    align-items: center;
}

.profile-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.school-name {
    font-size: 20px;
    margin-left: 10px;
}

.language {
    margin: 10px;
}

.flag {
    width: 20px;
    height: 20px;
    margin-left: 10px;
}

.icons img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.sidebar {
    width: 250px;
    background-color: #ffffff;
    padding: 20px;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
}

.new-record {
    background-color: #ffc107;
    border: none;
    padding: 10px;
    width: 100%;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;
}

.nav-menu a {
    display: block;
    color: #333;
    text-decoration: none;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
    transition: background-color 0.3s;
}

.nav-menu a:hover {
    background-color: #f1f1f1;
}

.main-content {
    flex: 1;
    padding: 20px;
}

.welcome, .card, .financials, .date, .expenses-type, .registrations {
    background-color: #ffffff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.stats, .financials {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.card {
    flex: 1;
    text-align: center;
    margin: 0 10px;
}

.card.parents {
    border: 2px solid #ffc107;
}

.card.batches {
    border: 2px solid #17a2b8;
}

.card.teachers {
    border: 2px solid #28a745;
}

.card.students {
    border: 2px solid #007bff;
}

.tabs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.tabs button {
    background-color: #ffffff;
    border: none;
    padding: 10px;
    flex: 1;
    margin: 0 5px;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s;
}

.tabs button.active, .tabs button:hover {
    background-color: #007bff;
    color: white;
}

.registrations ul {
    list-style-type: none;
    padding: 0;
}

.registrations ul li {
    margin-bottom: 10px;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    display: none;
    position: absolute;
    right: 0;
    background-color: white;
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    text-align: right;
    direction: rtl;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-header {
    text-align: center;
    padding: 10px;
}

.dropdown-header .profile-pic {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
}

.dropdown-item {
    padding: 10px 20px;
    display: block;
    color: #333;
    text-decoration: none;
}

.dropdown-item:hover {
    background-color: #f1f1f1;
}

.dropdown-divider {
    height: 1px;
    margin: 5px 0;
    overflow: hidden;
    background-color: #e5e5e5;
}

.search-container {
    margin: 20px 0;
}

.search-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.search-input {
    padding: 10px;
    width: 200px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-left: 10px;
}

.search-button, .reset-button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-left: 10px;
    display: flex;
    align-items: center;
}

.search-button {
    background-color: #ffc107;
    color: #fff;
}

.reset-button {
    background-color: #6c757d;
    color: #fff;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

thead {
    background-color: #f1f1f1;
}

th, td {
    padding: 15px;
    text-align: center;
    border: 1px solid #ddd;
}

.actions button {
    background: none;
    border: none;
    cursor: pointer;
    margin-left: 10px;
}

.status .active {
    color: #28a745;
    font-weight: bold;
}

.total {
    text-align: left;
    font-size: 16px;
    color: #333;
}

.between {
    justify-content: space-between;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 3% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    border-radius: 10px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.topclose {
    text-align: end;
}

.close:hover, .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

form {
    display: flex;
    flex-direction: column;
}

button {
    background-color: #ffcc00;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
}

button:hover {
    background-color: #ff9900;
}

.downclose:hover, .downclose:focus {
    border: none;
    padding: 0px 20px 10px 20px;
    border-radius: 5px;
}

::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 10px;
    border: 3px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}

* {
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
}

.scroll {
    unicode-bidi: bidi-override;
    direction: rtl;
    overflow: scroll;
    overflow-x: hidden !important;
}

html, body {
    height: 100%;
    overflow: hidden;
}

body {
    padding: 0;
    margin: 0;
    overflow-y: auto;
}


