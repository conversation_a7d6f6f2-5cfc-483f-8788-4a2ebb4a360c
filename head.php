<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap');
        body {
            font-family: 'Cairo', sans-serif;
        }
        .header {
            background-color: #2c3e50;
            color: white;
        }
        .profile-pic {
            width: 40px;
            height: 40px;
        }
        .flag {
            width: 24px;
            height: 16px;
        }
        .header-left, .header-right {
            flex: 1;
        }
        .sidebar {
            background-color: #f8f9fa;
            padding: 15px;
        }
        .nav-menu a {
            display: block;
            padding: 10px 15px;
            color: #343a40;
            text-decoration: none;
        }
        .nav-menu a:hover {
            background-color: #e9ecef;
        }
        .main-content {
            padding: 20px;
        }
        .stats, .financials {
            display: flex;
            gap: 15px;
        }
        .card {
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            flex: 1;
        }
        .tabs button {
            background-color: #2c3e50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
        }
        .tabs button.active {
            background-color: #1abc9c;
        }
        .logoim {
            width: 150px;
            margin-left: 90px;
            background-color: #f3f7fa;
            border-radius: 10px;
            }  




/* تخصيص الشريط بأكمله */
::-webkit-scrollbar {
    width: 12px; /* عرض الشريط */
}

/* تخصيص الخلفية للشريط */
::-webkit-scrollbar-track {
    background: #f1f1f1; /* لون خلفية الشريط */
}

/* تخصيص المقبض الذي يتحرك */
::-webkit-scrollbar-thumb {
    background-color: #888; /* لون المقبض */
    border-radius: 10px; /* تقويس المقبض */
    border: 3px solid #f1f1f1; /* لون الحدود حول المقبض */
}

/* عند تمرير الفأرة فوق الشريط */
::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* لون المقبض عند التمرير */
}

/* تخصيص المقبض */
* {
    scrollbar-width: thin; /* تحديد سماكة الشريط */
    scrollbar-color: #888 #f1f1f1; /* لون المقبض والخلفية */
}

.scroll {
            unicode-bidi:bidi-override;
            direction: rtl;
            overflow: scroll;
            overflow-x: hidden!important;
        }

        html, body {
      height: 100%;
      overflow: hidden;
    }

    body {
      padding: 0;
      margin: 0;
      overflow-y: auto;
    }
    </style>
</head>