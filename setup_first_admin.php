<?php
include "db_conn.php";

// التحقق من وجود مستخدمين في النظام
$check_sql = "SELECT COUNT(*) as user_count FROM users";
$result = mysqli_query($conn, $check_sql);
$row = mysqli_fetch_assoc($result);

if ($row['user_count'] > 0) {
    // يوجد مستخدمين بالفعل، التوجيه إلى صفحة تسجيل الدخول
    header("Location: login_professional.php?error=النظام مُعد بالفعل. يرجى تسجيل الدخول");
    exit();
}

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من البيانات وإنشاء المستخدم
    $username = $_POST['username'];
    $password = $_POST['password'];
    $name = $_POST['name'];
    $email = $_POST['email'];
    
    // التحقق من صحة البيانات
    if (empty($username) || empty($password) || empty($name) || empty($email)) {
        $error = "جميع الحقول مطلوبة";
    } else {
        // إنشاء المستخدم
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        $insert_sql = "INSERT INTO users (user_name, password, name, email, role, status, created_at) 
                      VALUES (?, ?, ?, ?, 'admin', 'active', NOW())";
        $stmt = mysqli_prepare($conn, $insert_sql);
        mysqli_stmt_bind_param($stmt, "ssss", $username, $hashed_password, $name, $email);
        
        if (mysqli_stmt_execute($stmt)) {
            // تم إنشاء المستخدم بنجاح
            header("Location: login_professional.php?success=تم إنشاء حساب المدير بنجاح. يمكنك الآن تسجيل الدخول");
            exit();
        } else {
            $error = "حدث خطأ أثناء إنشاء الحساب: " . mysqli_error($conn);
        }
        
        mysqli_stmt_close($stmt);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد حساب المدير - مركز آفاق التعليمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .setup-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .setup-title {
            text-align: center;
            margin-bottom: 30px;
            color: #3498db;
        }
        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <h2 class="setup-title">إعداد حساب المدير الأول</h2>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="name" class="form-label">الاسم الكامل</label>
                    <input type="text" class="form-control" id="name" name="name" value="محمد أحمد العلوي" required>
                </div>
                
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" name="username" value="admin_afaq" required>
                </div>
                
                <div class="mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" id="password" name="password" value="Afaq@2024" required>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">إنشاء حساب المدير</button>
            </form>
        </div>
    </div>
</body>
</html>