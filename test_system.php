<?php
// ملف اختبار النظام
session_start();

echo "<h1>اختبار النظام</h1>";

// اختبار الاتصال بقاعدة البيانات
include "db_conn.php";

if ($conn) {
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // اختبار وجود جدول المستخدمين
    $check_table = "SHOW TABLES LIKE 'users'";
    $result = mysqli_query($conn, $check_table);
    
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✅ جدول المستخدمين موجود</p>";
        
        // عرض عدد المستخدمين
        $count_users = "SELECT COUNT(*) as count FROM users";
        $result = mysqli_query($conn, $count_users);
        $count = mysqli_fetch_assoc($result)['count'];
        echo "<p>عدد المستخدمين: $count</p>";
        
        // عرض المستخدمين
        $get_users = "SELECT id, user_name, name, role, status FROM users";
        $result = mysqli_query($conn, $get_users);
        
        if (mysqli_num_rows($result) > 0) {
            echo "<h3>المستخدمون الموجودون:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم</th><th>الدور</th><th>الحالة</th></tr>";
            
            while ($user = mysqli_fetch_assoc($result)) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . htmlspecialchars($user['user_name']) . "</td>";
                echo "<td>" . htmlspecialchars($user['name']) . "</td>";
                echo "<td>" . htmlspecialchars($user['role']) . "</td>";
                echo "<td>" . htmlspecialchars($user['status']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ جدول المستخدمين غير موجود</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات</p>";
}

// اختبار الجلسة
echo "<h3>معلومات الجلسة:</h3>";
if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✅ المستخدم مسجل الدخول</p>";
    echo "<p>ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>اسم المستخدم: " . htmlspecialchars($_SESSION['user_name']) . "</p>";
    echo "<p>الاسم: " . htmlspecialchars($_SESSION['name']) . "</p>";
} else {
    echo "<p style='color: orange;'>⚠️ المستخدم غير مسجل الدخول</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>العودة إلى صفحة تسجيل الدخول</a></p>";
echo "<p><a href='setup_admin.php'>إعداد المدير</a></p>";
echo "<p><a href='home.php'>الصفحة الرئيسية</a></p>";
?>
