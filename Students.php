<!DOCTYPE html>
<html lang="ar">
<?php 
session_start();
if (isset($_SESSION['id']) && isset($_SESSION['user_name'])) {
?>
<?php include 'head.php'; ?>
<body>
	<?php include 'topmenu.php'; ?>
    <div class="container-fluid mt-3">
        <div class="row">
	<?php include 'rmenu.php'; ?>


    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close topclose">&times;</span>
            <h2>أضف طالبًا جديدًا</h2>
            <form id="addStudentForm">
                <label for="firstName">الاسم بالعربية:</label>
                <input type="text" id="firstName" name="firstName" required>
                <label for="lastName">النسب بالعربية:</label>
                <input type="text" id="lastName" name="lastName" required>
                <label for="firstNameFr">الاسم بالفرنسية:</label>
                <input type="text" id="firstNameFr" name="firstNameFr" required>
                <label for="lastNameFr">النسب بالفرنسية:</label>
                <input type="text" id="lastNameFr" name="lastNameFr" required>
                <label for="gender">الجنس:</label>
                <div>
                    <input type="radio" id="male" name="gender" value="ذكر" required>
                    <label for="male">ذكر</label>
                    <input type="radio" id="female" name="gender" value="أنثى">
                    <label for="female">أنثى</label>
                </div>
                <label for="birthdate">تاريخ الميلاد:</label>
                <input type="date" id="birthdate" name="birthdate" required>
                <label for="contactInfo">معلومات الاتصال:</label>
                <input type="text" id="contactInfo" name="contactInfo">
                <button type="submit">تسجيل</button>
                <button type="button" class="close downclose">إغلاق</button>
            </form>
        </div>
    </div>

    <main class="main-content">
        <div class="d-flex between">
            <h2>إدارة الطلاب</h2>
            <button class="add-button" id="addButton"><i class="fas fa-plus"></i> إضافة</button>
        </div>
        <div class="content">
            <div class="search-bar">
                <input type="text" placeholder="بحث عن اسم" class="search-input">
                <input type="text" placeholder="بحث عن النسب" class="search-input">
                <input type="text" placeholder="بحث عن المرجع" class="search-input">
                <button class="search-button"><i class="fas fa-search"></i> بحث</button>
                <button class="reset-button"><i class="fas fa-sync-alt"></i> إعادة ضبط</button>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>المرجع</th>
                        <th>الاسم</th>
                        <th>النسب</th>
                        <th>الهاتف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>E2</td>
                        <td>أيوب</td>
                        <td>بوجدال</td>
                        <td><a href="tel:+212662820474">+212 662 82 04 74</a><br><a href="https://wa.me/212662820474">+212 662 82 04 74</a></td>
                        <td class="actions">
                            <button><i class="fas fa-trash"></i></button>
                            <button><i class="fas fa-edit"></i></button>
                            <button><i class="fas fa-eye"></i></button>
                        </td>
                        
                    </tr>
                    <tr>
                        <td>E1</td>
                        <td>أحمد</td>
                        <td>العلوي</td>
                        <td><a href="tel:+212609962880">+212 609 96 28 80</a><br><a href="https://wa.me/212609962880">+212 609 96 28 80</a></td>
                        <td class="actions">
                            <button><i class="fas fa-trash"></i></button>
                            <button><i class="fas fa-edit"></i></button>
                            <button><i class="fas fa-eye"></i></button>
                        </td>
                        
                    </tr>
                </tbody>
            </table>
        </div>

                
    <script src="script.js"></script>

    </main>


<?php include 'footer.php'; ?>
<?php 
}else{
     header("Location: index.php");
     exit();
}
 ?>