<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_name'])) {
    header("Location: index.php?error=يجب تسجيل الدخول أولاً");
    exit();
}

// Include database connection
include "db_conn.php";

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'];
$name = $_SESSION['name'];

// Check if user still exists and is active
$check_user_sql = "SELECT status, role FROM users WHERE id = ?";
$stmt = mysqli_prepare($conn, $check_user_sql);
if ($stmt) {
    mysqli_stmt_bind_param($stmt, "i", $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) === 0) {
        // User doesn't exist anymore
        session_destroy();
        header("Location: index.php?error=حسابك غير موجود");
        exit();
    }

    $user_data = mysqli_fetch_assoc($result);
    if ($user_data['status'] !== 'active') {
        // User is not active
        session_destroy();
        header("Location: index.php?error=حسابك غير مفعل");
        exit();
    }

    mysqli_stmt_close($stmt);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية - مركز آفاق التعليمي</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-text: #2c3e50;
            --light-text: #7f8c8d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--light-bg);
            direction: rtl;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            color: white !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: var(--warning-color) !important;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--warning-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .main-content {
            padding: 30px;
            min-height: calc(100vh - 80px);
        }

        .welcome-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        .welcome-icon {
            font-size: 4rem;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .welcome-title {
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .welcome-subtitle {
            color: var(--light-text);
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            transform: translateY(-5px);
            text-decoration: none;
            color: inherit;
        }

        .action-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }

        .action-icon.students { background: var(--success-color); }
        .action-icon.teachers { background: var(--warning-color); }
        .action-icon.classes { background: var(--accent-color); }
        .action-icon.reports { background: var(--secondary-color); }
        .action-icon.settings { background: var(--primary-color); }
        .action-icon.admin { background: linear-gradient(135deg, var(--accent-color), var(--warning-color)); }

        .action-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 8px;
        }

        .action-description {
            color: var(--light-text);
            font-size: 0.9rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--light-text);
            font-size: 0.9rem;
        }

        .footer {
            background: var(--primary-color);
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 50px;
        }

        @media (max-width: 768px) {
            .welcome-title {
                font-size: 2rem;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>
                مركز آفاق التعليمي
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <?php if (isset($user_data) && $user_data['role'] === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="admin.php">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link" href="Students.php">
                            <i class="fas fa-user-graduate me-1"></i>
                            الطلاب
                        </a>
                    </li>
                </ul>

                <div class="user-info">
                    <span>مرحباً، <?php echo htmlspecialchars($name); ?></span>
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($name, 0, 1)); ?>
                    </div>
                    <a href="logout.php" class="btn btn-outline-light btn-sm ms-2">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <!-- Welcome Section -->
            <div class="welcome-card">
                <div class="welcome-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1 class="welcome-title">مرحباً بك في مركز آفاق التعليمي</h1>
                <p class="welcome-subtitle">
                    نظام إدارة شامل للمؤسسات التعليمية الخاصة
                </p>

                <?php if (isset($_GET['success'])): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($_GET['success']); ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <h2 class="mb-4" style="color: var(--primary-color); font-weight: 600;">
                <i class="fas fa-bolt me-2"></i>
                الإجراءات السريعة
            </h2>

            <div class="quick-actions">
                <a href="Students.php" class="action-card">
                    <div class="action-icon students">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="action-title">إدارة الطلاب</div>
                    <div class="action-description">عرض وإدارة معلومات الطلاب والتسجيلات</div>
                </a>

                <a href="teachers.php" class="action-card">
                    <div class="action-icon teachers">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="action-title">إدارة الأساتذة</div>
                    <div class="action-description">إدارة معلومات الأساتذة والتخصصات</div>
                </a>

                <a href="classes.php" class="action-card">
                    <div class="action-icon classes">
                        <i class="fas fa-school"></i>
                    </div>
                    <div class="action-title">إدارة الفصول</div>
                    <div class="action-description">تنظيم الفصول والجداول الدراسية</div>
                </a>

                <a href="reports.php" class="action-card">
                    <div class="action-icon reports">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="action-title">التقارير</div>
                    <div class="action-description">عرض التقارير والإحصائيات</div>
                </a>

                <a href="settings.php" class="action-card">
                    <div class="action-icon settings">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="action-title">الإعدادات</div>
                    <div class="action-description">إعدادات النظام والتخصيص</div>
                </a>

                <?php if (isset($user_data) && $user_data['role'] === 'admin'): ?>
                <a href="admin.php" class="action-card">
                    <div class="action-icon admin">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="action-title">لوحة تحكم المدير</div>
                    <div class="action-description">إدارة شاملة للنظام والمستخدمين</div>
                </a>
                <?php endif; ?>
            </div>

            <!-- Statistics -->
            <h2 class="mb-4" style="color: var(--primary-color); font-weight: 600;">
                <i class="fas fa-chart-pie me-2"></i>
                إحصائيات سريعة
            </h2>

            <div class="stats-grid">
                <?php
                // Get quick statistics
                $stats = [];

                // Count students
                $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM students WHERE status = 'active'");
                $stats['students'] = $result ? mysqli_fetch_assoc($result)['count'] : 0;

                // Count teachers
                $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM teachers WHERE status = 'active'");
                $stats['teachers'] = $result ? mysqli_fetch_assoc($result)['count'] : 0;

                // Count classes
                $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM classes WHERE status = 'active'");
                $stats['classes'] = $result ? mysqli_fetch_assoc($result)['count'] : 0;

                // Count users
                $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM users WHERE status = 'active'");
                $stats['users'] = $result ? mysqli_fetch_assoc($result)['count'] : 0;
                ?>

                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['students']; ?></div>
                    <div class="stat-label">الطلاب النشطون</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['teachers']; ?></div>
                    <div class="stat-label">الأساتذة النشطون</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['classes']; ?></div>
                    <div class="stat-label">الفصول النشطة</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['users']; ?></div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 مركز آفاق التعليمي. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>