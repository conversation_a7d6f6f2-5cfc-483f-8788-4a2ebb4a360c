<?php
session_start();

// Include database connection
include "db_conn.php";

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Rate limiting variables
$max_attempts = 5;
$lockout_time = 900; // 15 minutes in seconds

/**
 * Validate and sanitize input data
 */
function validate($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Log login attempts for security monitoring
 */
function logLoginAttempt($username, $ip, $success, $conn) {
    $sql = "INSERT INTO login_attempts (username, ip_address, success, attempt_time) VALUES (?, ?, ?, NOW())";
    $stmt = mysqli_prepare($conn, $sql);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "ssi", $username, $ip, $success);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    }
}

/**
 * Check if IP is currently locked out
 */
function isLockedOut($ip, $conn, $max_attempts, $lockout_time) {
    $sql = "SELECT COUNT(*) as attempts FROM login_attempts 
            WHERE ip_address = ? AND success = 0 
            AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)";
    
    $stmt = mysqli_prepare($conn, $sql);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "si", $ip, $lockout_time);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        return $row['attempts'] >= $max_attempts;
    }
    return false;
}

/**
 * Clean old login attempts (older than 24 hours)
 */
function cleanOldAttempts($conn) {
    $sql = "DELETE FROM login_attempts WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    mysqli_query($conn, $sql);
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: login_professional.php?error=طريقة الوصول غير صحيحة");
    exit();
}

// Check if required fields are present
if (!isset($_POST['uname']) || !isset($_POST['password'])) {
    header("Location: login_professional.php?error=جميع الحقول مطلوبة");
    exit();
}

// Get user IP address
$user_ip = $_SERVER['REMOTE_ADDR'];
if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $user_ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
} elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
    $user_ip = $_SERVER['HTTP_X_REAL_IP'];
}

// Clean old login attempts
cleanOldAttempts($conn);

// Check if IP is locked out
if (isLockedOut($user_ip, $conn, $max_attempts, $lockout_time)) {
    logLoginAttempt('', $user_ip, 0, $conn);
    header("Location: login_professional.php?error=تم حظر عنوان IP الخاص بك مؤقتاً بسبب محاولات دخول متعددة فاشلة. حاول مرة أخرى بعد 15 دقيقة");
    exit();
}

// Validate and sanitize input
$username = validate($_POST['uname']);
$password = validate($_POST['password']);

// Check for empty fields
if (empty($username)) {
    logLoginAttempt($username, $user_ip, 0, $conn);
    header("Location: login_professional.php?error=اسم المستخدم مطلوب");
    exit();
}

if (empty($password)) {
    logLoginAttempt($username, $user_ip, 0, $conn);
    header("Location: login_professional.php?error=كلمة المرور مطلوبة");
    exit();
}

// Additional validation
if (strlen($username) < 3) {
    logLoginAttempt($username, $user_ip, 0, $conn);
    header("Location: login_professional.php?error=اسم المستخدم يجب أن يكون 3 أحرف على الأقل");
    exit();
}

if (strlen($password) < 6) {
    logLoginAttempt($username, $user_ip, 0, $conn);
    header("Location: login_professional.php?error=كلمة المرور يجب أن تكون 6 أحرف على الأقل");
    exit();
}

try {
    // Prepare SQL statement to prevent SQL injection
    $sql = "SELECT id, user_name, password, name, status, last_login FROM users WHERE user_name = ? LIMIT 1";
    $stmt = mysqli_prepare($conn, $sql);
    
    if (!$stmt) {
        throw new Exception("خطأ في إعداد الاستعلام");
    }
    
    mysqli_stmt_bind_param($stmt, "s", $username);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) === 1) {
        $user = mysqli_fetch_assoc($result);
        
        // Check if account is active
        if (isset($user['status']) && $user['status'] === 'inactive') {
            logLoginAttempt($username, $user_ip, 0, $conn);
            header("Location: login_professional.php?error=حسابك غير مفعل. يرجى الاتصال بالإدارة");
            exit();
        }
        
        // Verify password (support both MD5 legacy and new password_hash)
        $password_valid = false;
        
        // Check if it's a new password hash
        if (password_get_info($user['password'])['algo'] !== null) {
            $password_valid = password_verify($password, $user['password']);
        } else {
            // Legacy MD5 check
            $password_valid = ($user['password'] === md5($password));
            
            // If MD5 password is valid, upgrade to secure hash
            if ($password_valid) {
                $new_hash = password_hash($password, PASSWORD_DEFAULT);
                $update_sql = "UPDATE users SET password = ? WHERE id = ?";
                $update_stmt = mysqli_prepare($conn, $update_sql);
                if ($update_stmt) {
                    mysqli_stmt_bind_param($update_stmt, "si", $new_hash, $user['id']);
                    mysqli_stmt_execute($update_stmt);
                    mysqli_stmt_close($update_stmt);
                }
            }
        }
        
        if ($password_valid) {
            // Successful login
            
            // Regenerate session ID for security
            session_regenerate_id(true);
            
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['user_name'];
            $_SESSION['name'] = $user['name'];
            $_SESSION['login_time'] = time();
            $_SESSION['ip_address'] = $user_ip;
            
            // Update last login time
            $update_login_sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
            $update_stmt = mysqli_prepare($conn, $update_login_sql);
            if ($update_stmt) {
                mysqli_stmt_bind_param($update_stmt, "i", $user['id']);
                mysqli_stmt_execute($update_stmt);
                mysqli_stmt_close($update_stmt);
            }
            
            // Log successful login
            logLoginAttempt($username, $user_ip, 1, $conn);
            
            // Handle "Remember Me" functionality
            if (isset($_POST['remember']) && $_POST['remember'] === 'on') {
                $token = bin2hex(random_bytes(32));
                $expires = time() + (30 * 24 * 60 * 60); // 30 days
                
                // Store token in database
                $token_sql = "INSERT INTO remember_tokens (user_id, token, expires) VALUES (?, ?, FROM_UNIXTIME(?))
                             ON DUPLICATE KEY UPDATE token = VALUES(token), expires = VALUES(expires)";
                $token_stmt = mysqli_prepare($conn, $token_sql);
                if ($token_stmt) {
                    mysqli_stmt_bind_param($token_stmt, "isi", $user['id'], $token, $expires);
                    mysqli_stmt_execute($token_stmt);
                    mysqli_stmt_close($token_stmt);
                    
                    // Set cookie
                    setcookie('remember_token', $token, $expires, '/', '', true, true);
                }
            }
            
            // Redirect to dashboard
            header("Location: home.php?success=تم تسجيل الدخول بنجاح");
            exit();
            
        } else {
            // Invalid password
            logLoginAttempt($username, $user_ip, 0, $conn);
            header("Location: login_professional.php?error=اسم المستخدم أو كلمة المرور غير صحيحة");
            exit();
        }
        
    } else {
        // User not found
        logLoginAttempt($username, $user_ip, 0, $conn);
        header("Location: login_professional.php?error=اسم المستخدم أو كلمة المرور غير صحيحة");
        exit();
    }
    
    mysqli_stmt_close($stmt);
    
} catch (Exception $e) {
    // Log error for debugging (in production, log to file)
    error_log("Login error: " . $e->getMessage());
    
    logLoginAttempt($username, $user_ip, 0, $conn);
    header("Location: login_professional.php?error=حدث خطأ في النظام. يرجى المحاولة مرة أخرى");
    exit();
}

// Close database connection
mysqli_close($conn);

// If we reach here, something went wrong
header("Location: login_professional.php?error=حدث خطأ غير متوقع");
exit();
?>
