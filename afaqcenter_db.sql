-- إن<PERSON>اء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS afaqcenter CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE afaqcenter;

-- جدول المستخدمين (users)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    role ENUM('admin', 'teacher', 'staff', 'user') DEFAULT 'user',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME NULL,
    phone VARCHAR(50) NULL,
    profile_image VARCHAR(255) NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ج<PERSON><PERSON><PERSON> الطلاب (students)
CREATE TABLE students (
    student_id INT AUTO_INCREMENT PRIMARY KEY,
    reference_number VARCHAR(50) UNIQUE,
    first_name_ar VARCHAR(100) NOT NULL,
    last_name_ar VARCHAR(100) NOT NULL,
    first_name_fr VARCHAR(100),
    last_name_fr VARCHAR(100),
    gender ENUM('ذكر', 'أنثى') NOT NULL,
    date_of_birth DATE,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    parent_id INT,
    enrollment_date DATE DEFAULT CURRENT_DATE,
    grade_level VARCHAR(50),
    status ENUM('نشط', 'متوقف', 'متخرج') DEFAULT 'نشط',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول أولياء الأمور (parents)
CREATE TABLE parents (
    parent_id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    relationship ENUM('أب', 'أم', 'وصي', 'أخرى') DEFAULT 'أب',
    phone_primary VARCHAR(50) NOT NULL,
    phone_secondary VARCHAR(50),
    email VARCHAR(255),
    address TEXT,
    occupation VARCHAR(100),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة العلاقة بين الطلاب وأولياء الأمور
ALTER TABLE students
ADD CONSTRAINT fk_student_parent
FOREIGN KEY (parent_id) REFERENCES parents(parent_id)
ON DELETE SET NULL;

-- جدول المعلمين (teachers)
CREATE TABLE teachers (
    teacher_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    gender ENUM('ذكر', 'أنثى') NOT NULL,
    date_of_birth DATE,
    address TEXT,
    phone VARCHAR(50) NOT NULL,
    email VARCHAR(255),
    specialization VARCHAR(100),
    qualification VARCHAR(255),
    hire_date DATE DEFAULT CURRENT_DATE,
    employment_type ENUM('دوام كامل', 'دوام جزئي', 'بالساعة') DEFAULT 'دوام كامل',
    salary DECIMAL(10, 2),
    hourly_rate DECIMAL(10, 2),
    status ENUM('نشط', 'متوقف') DEFAULT 'نشط',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الأقسام (departments)
CREATE TABLE departments (
    department_id INT AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL,
    description TEXT,
    head_teacher_id INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (head_teacher_id) REFERENCES teachers(teacher_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المواد الدراسية (subjects)
CREATE TABLE subjects (
    subject_id INT AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(20) UNIQUE,
    department_id INT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الفصول الدراسية (classes)
CREATE TABLE classes (
    class_id INT AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(100) NOT NULL,
    grade_level VARCHAR(50),
    academic_year VARCHAR(20),
    capacity INT DEFAULT 30,
    room_number VARCHAR(20),
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تسجيل الطلاب في الفصول (class_enrollments)
CREATE TABLE class_enrollments (
    enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT,
    class_id INT,
    enrollment_date DATE DEFAULT CURRENT_DATE,
    status ENUM('نشط', 'متوقف', 'مكتمل') DEFAULT 'نشط',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(class_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الحصص الدراسية (lessons)
CREATE TABLE lessons (
    lesson_id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT,
    teacher_id INT,
    class_id INT,
    day_of_week ENUM('الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'),
    start_time TIME,
    end_time TIME,
    room VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(class_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الحضور (attendance)
CREATE TABLE attendance (
    attendance_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT,
    lesson_id INT,
    date DATE NOT NULL,
    status ENUM('حاضر', 'غائب', 'متأخر', 'مستأذن') DEFAULT 'حاضر',
    notes TEXT,
    recorded_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES lessons(lesson_id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الفواتير (invoices)
CREATE TABLE invoices (
    invoice_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT,
    invoice_number VARCHAR(50) UNIQUE,
    amount DECIMAL(10, 2) NOT NULL,
    issue_date DATE DEFAULT CURRENT_DATE,
    due_date DATE,
    status ENUM('غير مدفوعة', 'مدفوعة جزئياً', 'مدفوعة') DEFAULT 'غير مدفوعة',
    description TEXT,
    created_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المدفوعات (payments)
CREATE TABLE payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT,
    amount DECIMAL(10, 2) NOT NULL,
    payment_date DATE DEFAULT CURRENT_DATE,
    payment_method ENUM('نقداً', 'شيك', 'تحويل بنكي', 'أخرى') DEFAULT 'نقداً',
    reference_number VARCHAR(100),
    notes TEXT,
    received_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id) ON DELETE CASCADE,
    FOREIGN KEY (received_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المصروفات (expenses)
CREATE TABLE expenses (
    expense_id INT AUTO_INCREMENT PRIMARY KEY,
    expense_category VARCHAR(100) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    expense_date DATE DEFAULT CURRENT_DATE,
    description TEXT,
    payment_method ENUM('نقداً', 'شيك', 'تحويل بنكي', 'أخرى') DEFAULT 'نقداً',
    reference_number VARCHAR(100),
    approved_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول رواتب المعلمين (teacher_salaries)
CREATE TABLE teacher_salaries (
    salary_id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT,
    month INT NOT NULL,
    year INT NOT NULL,
    base_salary DECIMAL(10, 2) NOT NULL,
    bonus DECIMAL(10, 2) DEFAULT 0,
    deductions DECIMAL(10, 2) DEFAULT 0,
    total_amount DECIMAL(10, 2) GENERATED ALWAYS AS (base_salary + bonus - deductions) STORED,
    payment_date DATE,
    payment_status ENUM('معلق', 'مدفوع') DEFAULT 'معلق',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التقييمات (evaluations)
CREATE TABLE evaluations (
    evaluation_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT,
    subject_id INT,
    evaluation_type ENUM('اختبار', 'واجب', 'مشروع', 'تقييم شهري', 'تقييم نهائي') NOT NULL,
    score DECIMAL(5, 2) NOT NULL,
    max_score DECIMAL(5, 2) NOT NULL,
    evaluation_date DATE DEFAULT CURRENT_DATE,
    notes TEXT,
    teacher_id INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول العطل (holidays)
CREATE TABLE holidays (
    holiday_id INT AUTO_INCREMENT PRIMARY KEY,
    holiday_name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإشعارات (notifications)
CREATE TABLE notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء المشغلات (Triggers)
DELIMITER //

-- مشغل لتحديث حالة الفاتورة عند إضافة دفعة
CREATE TRIGGER update_invoice_status
AFTER INSERT ON payments
FOR EACH ROW
BEGIN
    DECLARE invoice_total DECIMAL(10, 2);
    DECLARE payments_total DECIMAL(10, 2);
    
    -- الحصول على إجمالي الفاتورة
    SELECT amount INTO invoice_total 
    FROM invoices 
    WHERE invoice_id = NEW.invoice_id;
    
    -- حساب إجمالي المدفوعات
    SELECT SUM(amount) INTO payments_total 
    FROM payments 
    WHERE invoice_id = NEW.invoice_id;
    
    -- تحديث حالة الفاتورة
    IF payments_total >= invoice_total THEN
        UPDATE invoices SET status = 'مدفوعة' WHERE invoice_id = NEW.invoice_id;
    ELSE
        UPDATE invoices SET status = 'مدفوعة جزئياً' WHERE invoice_id = NEW.invoice_id;
    END IF;
END //

-- مشغل لإنشاء حساب مستخدم عند إضافة معلم جديد
CREATE TRIGGER create_teacher_user
BEFORE INSERT ON teachers
FOR EACH ROW
BEGIN
    DECLARE new_user_id INT;
    
    -- إنشاء حساب مستخدم للمعلم إذا لم يكن موجوداً
    IF NEW.user_id IS NULL THEN
        -- إنشاء اسم مستخدم فريد
        SET @username = CONCAT(LOWER(REPLACE(NEW.first_name, ' ', '')), '_', LOWER(REPLACE(NEW.last_name, ' ', '')));
        
        -- إنشاء كلمة مرور افتراضية (يجب تغييرها لاحقاً)
        SET @default_password = CONCAT(LOWER(REPLACE(NEW.first_name, ' ', '')), '@', YEAR(CURRENT_DATE));
        
        -- إدراج المستخدم الجديد
        INSERT INTO users (user_name, password, name, email, role, status, phone)
        VALUES (@username, PASSWORD(@default_password), CONCAT(NEW.first_name, ' ', NEW.last_name), NEW.email, 'teacher', 'active', NEW.phone);
        
        -- الحصول على معرف المستخدم الجديد
        SET new_user_id = LAST_INSERT_ID();
        
        -- تعيين معرف المستخدم للمعلم
        SET NEW.user_id = new_user_id;
    END IF;
END //

DELIMITER ;

-- إنشاء الإجراءات المخزنة (Stored Procedures)
DELIMITER //

-- إجراء لإضافة طالب جديد
CREATE PROCEDURE AddNewStudent(
    IN p_reference_number VARCHAR(50),
    IN p_first_name_ar VARCHAR(100),
    IN p_last_name_ar VARCHAR(100),
    IN p_first_name_fr VARCHAR(100),
    IN p_last_name_fr VARCHAR(100),
    IN p_gender ENUM('ذكر', 'أنثى'),
    IN p_date_of_birth DATE,
    IN p_address TEXT,
    IN p_phone VARCHAR(50),
    IN p_email VARCHAR(255),
    IN p_parent_id INT,
    IN p_grade_level VARCHAR(50)
)
BEGIN
    INSERT INTO students (
        reference_number, first_name_ar, last_name_ar, first_name_fr, last_name_fr,
        gender, date_of_birth, address, phone, email, parent_id, grade_level
    )
    VALUES (
        p_reference_number, p_first_name_ar, p_last_name_ar, p_first_name_fr, p_last_name_fr,
        p_gender, p_date_of_birth, p_address, p_phone, p_email, p_parent_id, p_grade_level
    );
END //

-- إجراء لإنشاء فاتورة جديدة
CREATE PROCEDURE CreateInvoice(
    IN p_student_id INT,
    IN p_amount DECIMAL(10, 2),
    IN p_due_date DATE,
    IN p_description TEXT,
    IN p_created_by INT
)
BEGIN
    DECLARE new_invoice_number VARCHAR(50);
    
    -- إنشاء رقم فاتورة فريد
    SET new_invoice_number = CONCAT('INV-', YEAR(CURRENT_DATE), '-', LPAD((SELECT COUNT(*) + 1 FROM invoices WHERE YEAR(issue_date) = YEAR(CURRENT_DATE)), 4, '0'));
    
    -- إدراج الفاتورة الجديدة
    INSERT INTO invoices (
        student_id, invoice_number, amount, issue_date, due_date, description, created_by
    )
    VALUES (
        p_student_id, new_invoice_number, p_amount, CURRENT_DATE, p_due_date, p_description, p_created_by
    );
    
    -- إرجاع رقم الفاتورة الجديد
    SELECT new_invoice_number AS invoice_number;
END //

-- إجراء لتسجيل حضور الطلاب
CREATE PROCEDURE RecordAttendance(
    IN p_lesson_id INT,
    IN p_date DATE,
    IN p_recorded_by INT
)
BEGIN
    -- إدراج سجلات الحضور لجميع الطلاب في الفصل
    INSERT INTO attendance (student_id, lesson_id, date, status, recorded_by)
    SELECT 
        ce.student_id, p_lesson_id, p_date, 'حاضر', p_recorded_by
    FROM 
        class_enrollments ce
    JOIN 
        lessons l ON ce.class_id = l.class_id
    WHERE 
        l.lesson_id = p_lesson_id
        AND ce.status = 'نشط'
        AND NOT EXISTS (
            SELECT 1 FROM attendance a 
            WHERE a.student_id = ce.student_id 
            AND a.lesson_id = p_lesson_id 
            AND a.date = p_date
        );
END //

DELIMITER ;

-- إنشاء المستخدم الأول (المدير)
INSERT INTO users (user_name, password, name, email, role, status, created_at)
VALUES ('admin_afaq', '$2y$10$XDYUVYODYzOGQ5MmM4ZDJlZWRkMGE1ZTc4ZTc3ZjU3NzE2YTY=', 'محمد أحمد العلوي', '<EMAIL>', 'admin', 'active', NOW());

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_student_name ON students(first_name_ar, last_name_ar);
CREATE INDEX idx_student_reference ON students(reference_number);
CREATE INDEX idx_teacher_name ON teachers(first_name, last_name);
CREATE INDEX idx_invoice_status ON invoices(status);
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_lesson_schedule ON lessons(day_of_week, start_time);